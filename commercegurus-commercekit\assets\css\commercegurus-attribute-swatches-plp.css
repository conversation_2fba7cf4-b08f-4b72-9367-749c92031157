/* CSS Document */
.cgkit-as-wrap-plp .cgkit-attribute-swatches {
	padding: 0px;
	margin: 0px;
}
.cgkit-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}
.cgkit-attribute-swatches-wrap legend {
	font-size: 0px;
    padding: 0;
}
/* Prevent flex equal heights for the product grid when PLP swatches are active */
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch {
	list-style: none;
	display: inline-block;
	padding: 0;
	margin: 0;
	vertical-align: top;
	line-height: 0;
	margin:  0 5px 5px 0;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.ckit-button {
	margin: 0 4px 4px 0;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch > button {
	vertical-align: top;
    display: block;
    position: relative;
	text-decoration: none;
    font-weight: 400;
}
ul.products li.product.cgkit-swatch-hover {
	align-self: flex-start;
}
ul.products li.product.cgkit-swatch-hover:not(.product-category):focus-visible {
	outline: none;
	z-index: 2;
}
ul.products li.product.cgkit-swatch-hover:not(.product-category):focus-visible:before {
	outline: 0.25rem solid #2491ff;
	outline-offset: 0;
	visibility: visible;
    opacity: 1;
    transition: all 0.2s;
    -webkit-box-sizing: unset;
    box-sizing: unset;
}
ul.products li.product.cgkit-swatch-hover:focus-visible form.cgkit-swatch-form table.variations tr {
    display: table;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch > button span.cross {
	display: none;
	position: absolute;
	top: 0px;
	left: 0px;
	background: linear-gradient(to top left, rgba(0,0,0,0) 0%, rgba(0,0,0,0) calc(50% - 0.4px), rgba(0,0,0,0.5) 50%, rgba(0,0,0,0) calc(50% + 0.4px), rgba(0,0,0,0) 100%)
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-color > button.cgkit-disabled span.cross {
	display: block;
    width: 22px;
    height: 22px;
    position: absolute;
    top: 4px;
    left: 4px;
    z-index: 1;
	background: linear-gradient(to top left, rgba(0,0,0,0) 0%, rgba(0,0,0,0) calc(50% - 0.4px), rgba(0,0,0,0.6) 50%, rgba(0,0,0,0) calc(50% + 0.4px), rgba(0,0,0,0) 100%)
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch > button.cgkit-disabled {
	opacity: 0.3;
	cursor: not-allowed;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch > button.cgkit-disabled:active {
	pointer-events: none;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch > button.cgkit-disabled span.cross {
	display: block;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch > button.cgkit-as-outofstock {
	opacity: 0.1;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-button button.button-fluid {
	padding: 9px 13px;
	min-width: auto;
    min-height: auto;
    line-height: 1;
}
.variations .cgkit-as-wrap-plp .cgkit-chosen-attribute {
	font-weight: normal;
	font-size: 14px;
	letter-spacing: 0;
	text-transform: none;
	padding-left: 3px;
}
.variations .cgkit-as-wrap-plp .cgkit-chosen-attribute span {
	display: none;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-color > button:before {
	content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px solid #ccc;
    margin: 0;
	border-radius: 50%;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-color > button:hover:before {
	border-color: #353c4e;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-color > button.cgkit-disabled:hover:before {
	border-color: #ccc;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-color > button.cgkit-swatch-selected:before {
	border: 1px solid #353c4e;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-color > button span.cross {
	display: none;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-color span.color-div {
    display: block;
	border-radius: 50%;
	white-space: nowrap;
    margin: 0px;
    padding: 0px;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-image button {
	width: 100%;
	height: 100%;
	position: relative;
	overflow: hidden;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-image > button:before {
	content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px solid #ccc;
    margin: 0;
    z-index: 1;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-image > button:not(.cgkit-disabled):hover:before {
	border-color: #353c4e;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-image > button span.cross {
	width: 60px;
	height: 60px;
	z-index: 2;
	display: none;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-image > button.cgkit-swatch-selected:before {
	border: 1px solid #353c4e;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-image img {
    white-space: nowrap;
    display: block;
    margin: 0px;
    padding: 0px;
}
li.product:not(.product-category):hover li.cgkit-attribute-swatch img {
	transform: none;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-button {
	position: relative;
	margin: 0px 5px 5px 0px;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-button button {
	border: 1px solid #333;
    display: inline-block;
    padding: 0 7px;
    border-radius: 2px;
    background: #fff;
    font-size: 13px;
    line-height: 1;
    color: #333;
    white-space: nowrap;
    position: relative;
    min-width: 42px;
    min-height: 42px;
    line-height: 40px;
    text-align: center;
    transition: background 0.2s;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-button button:not(.cgkit-disabled):not(.cgkit-swatch-selected):hover {
	background-color: #eee;
}
.cgkit-as-wrap-plp .cgkit-swatch-title {
	font-weight: bold;
	font-size: 14px;
	display: block;
	margin: 10px 0;
}
form.variations_form.cgkit-swatch-form table.variations tr:first-child .cgkit-swatch-title {
	display: none;
}
form.variations_form.cgkit-swatch-form table.variations tr .cgkit-swatch-title {
	height: 0px;
	visibility: hidden;
}
form.variations_form.cgkit-swatch-form table.variations tr:nth-child(2) .cgkit-swatch-title {
	height: auto;
	visibility: visible;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-button > button span.cross {
	width: 100%;
	height: 100%;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-button button.cgkit-swatch-selected {
	background: #111;
	border-color: #111;
	color: #fff;
}
.cgkit-as-wrap-plp .cgkit-chosen-attribute.no-selection {
	opacity: 0.5;
	font-weight: normal;
	padding-left: 3px;
}
.woocommerce .cgkit-as-wrap-plp .cgkit-attribute-swatches,
.cgkit-as-wrap-plp .cgkit-attribute-swatches {
	margin: 0;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-image button {
	width: 50px;
	height: 50px;
	border-radius: 0;
	border: 1px solid transparent;
	transition: border 0.2s;
	box-sizing: border-box;
	background: unset;
	padding: 0px;
}
.cgkit-swatch-form table {
	margin: 0px !important;
	border: none;
}
.summary .cgkit-swatch-form table td,
.summary .cgkit-swatch-form table th {
	padding: 0.5em 0em;
}
li.product .cgkit-swatch-form table td,
li.product .cgkit-swatch-form table th {
	padding:  0;
}
li.product .cgkit-swatch-form table td.value {
	margin-bottom: 10px;
	border-width: 0;
	background: transparent;
}
li.product .cgkit-swatch-form table tr:last-child td.value {
	margin-bottom: 0;
}
.cgkit-swatch-form .woocommerce-variation-description,
.cgkit-swatch-form .woocommerce-variation-price,
.cgkit-swatch-form .woocommerce-variation-add-to-cart,
.cgkit-swatch-form .woocommerce-variation-availability > :not(.stock.out-of-stock){
	display: none;
}
ul.products li.product.cgkit-swatch-hover .cgkit-swatch-form .woocommerce-variation-availability .stock,
ul.products li.product.cgkit-swatch-hover .cgkit-swatch-form .woocommerce-variation-availability #ckwtl-button2 {
	display: none;
}
ul.products li.product.cgkit-swatch-hover:hover .cgkit-swatch-form .woocommerce-variation-availability .stock.out-of-stock {
	display: block;
}
ul.products li.product.cgkit-swatch-hover:hover .cgkit-swatch-form.cgkit-single-attribute .woocommerce-variation-availability .stock.out-of-stock {
	display: none;
}
.cgkit-swatch-form .woocommerce-variation-availability p {
	margin: 5px 0 0 0;
	font-size: 12px;
}
.product.cgkit-disable-atc .cgkit-swatch-form .stock.out-of-stock,
.product.cgkit-disable-atc .cgkit-swatch-form .wc-no-matching-variations {
	display: none !important;
}
@media (min-width: 993px) {
	.cgkit-swatch-form .woocommerce-variation-availability {
		position: absolute;
	    right: 0;
	    top: 12px;
	    margin: 0;
	}
}
ul.products li.product.cgkit-swatch-hover .woocommerce-variation.single_variation p {
	display: none;
}
ul.products li.product.cgkit-swatch-hover .wc-no-matching-variations {
	font-size: 13px;
    border: none;
    margin: 0;
    position: fixed;
    bottom: 10px;
    z-index: 5;
    right: 10px;
    margin-left: 10px;
}
.cgkit-swatch-form {
	position: relative;
}
.cgkit-swatch-form.loading {
	opacity: 0.5;
	pointer-events: none;
}
.cgkit-swatch-form:after {
	position: absolute;
	top: calc(50% - 15px);
	left: calc(50% - 15px);
	opacity: 0;
	-webkit-transition: opacity 0s ease;
	transition: opacity 0s ease;
	content: "";
	display: inline-block;
	width: 30px;
	height: 30px;
	border: 3px solid rgba(0, 0, 0, 0.5);
	border-left-color: #000;
	border-radius: 50%;
	vertical-align: middle;
	visibility: hidden;
	z-index: 11;
}
.cgkit-swatch-form.loading:after {
	opacity: 1;
	-webkit-transition: opacity 0.25s ease;
	transition: opacity 0.25s ease;
	webkit-animation: ckit-rotate 450ms infinite linear;
	animation: ckit-rotate 450ms infinite linear;
	visibility: visible;
}
#cgkit-as-notice-wrap {
	max-width: 100%;
	width: 300px;
	margin: 0;
	position: fixed;
	right: 20px;
	bottom: 20px;
	z-index: 10;
	font-size: 14px;
}
#cgkit-as-notice-wrap .cgkit-as-notice-close {
	position: absolute;
	right: 2px;
	top: 0px;
	color: #fff;
	z-index: 11;
	padding: 0px 5px;
	border-radius: 50%;
	text-transform: lowercase;
}
#cgkit-as-notice .woocommerce-error {
	background-color: #e2401c;
	padding: 1em 1.618em;
	border-left: 0px;
	margin-bottom: 0;
	font-size: 14px;
	border-radius: 4px;
	line-height: 1.5;
}
#cgkit-as-notice .woocommerce-error a {
    display: none;
}
li.product.cgkit-swatch-hover .images {
	width: 100%;
	margin-bottom: 0;
}
.cgkit-swatch-form, 
.cgkit-swatch-form table, 
.cgkit-swatch-form table tbody, 
.cgkit-swatch-form table tbody tr, 
.cgkit-swatch-form table tbody tr td {
	width: 100%;
	position: relative;
}
.cgkit-swatch-form .cgkit-as-swiper {
	width: calc(100% - 80px);
	overflow: hidden;
	position: relative;
	margin: 0 auto;
}
@media (min-width: 993px) {
	ul.products li.product.ckit-hide-cta:not(.product-category) {
		padding-bottom: 0; 
		margin-bottom: 30px;
	}
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch > button {
	width: 100%;
	display: block;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-color > button {
	width: 30px;
	height: 30px;
	margin: 0 auto;
	padding: 2px;
	background: unset;
	border: none;
	position: relative;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-color > button:focus,
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-color > button:focus-visible {
	border-radius: 50%;
}
.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-color span.color-div {
	width: 26px;
	height: 26px;
	text-indent: 100px;
	overflow: hidden;
}
.cgkit-swatch-form .swiper-button-next.swiper-button-disabled,
.cgkit-swatch-form .swiper-button-prev.swiper-button-disabled {
	visibility: hidden;
}
.cgkit-swatch-form .swiper-button-next,
.cgkit-swatch-form .swiper-button-prev {
	position: absolute !important;
	background: #fff;
	z-index: 1;
}
.cgkit-swatch-form .swiper-button-next:after,
.cgkit-swatch-form .swiper-button-prev:after {
	font-size: 11px;
    color: #222;
}
.cgkit-swatch-form .swiper-button-next,
.cgkit-swatch-form .swiper-container-rtl .swiper-button-prev {
	top: 0px;
	right: 0px;
	left: auto;
	height: 24px;
	width: 30px;
	margin-top: 0px;
}
.cgkit-swatch-form .swiper-button-prev,
.cgkit-swatch-form .swiper-container-rtl .swiper-button-next {
	top: 0px;
	right: auto;
	left: 0px;
	height: 24px;
	width: 30px;
	margin-top: 0px;
}
.cgkit-swatch-form .cgkit-as-swiper-tmp .swiper-button-next,
.cgkit-swatch-form .cgkit-as-swiper-tmp .swiper-button-prev {
	display: none;
}
.cgkit-swatch-form .cgkit-swatch-quickadd {
	display: none;
	position: relative;
	z-index: 2;
	width: 100%;
	padding: 10px;
	cursor: pointer;
	background-color:#fff;
	outline: none;
	border: 1px solid #d3d4d5;
    text-align: left;
	margin-bottom: 10px;
}
.cgkit-swatch-form .cgkit-swatch-quickadd p {
	padding: 0;
	margin: 0;
	color: #212a2f;
	font-weight: bold;
}
.cgkit-swatch-form .cgkit-swatch-quickadd i {
	position: absolute;
	right: 0px;
	top: 5px;
}
.cgkit-swatch-form .cgkit-swatch-quickadd i:after,
.cgkit-swatch-form .cgkit-swatch-quickadd i:before {
	content: "";
	position: absolute;
	border-left: 3px solid #212a2f;
	top: 12px;
	right: 24px;
	height: 12px;
	transition: transform 250ms ease 0s;
}
.cgkit-swatch-form .cgkit-swatch-quickadd i:after {
	transform: rotate(90deg);
}
.cgkit-swatch-form .cgkit-swatch-quickadd i:before {
	transform: rotate(0deg);
}
.cgkit-swatch-form .cgkit-swatch-quickadd.active i:before {
	transform: rotate(90deg);
}
body ul.products li.product.cgkit-swatch-hover form.cgkit-swatch-form table.variations tr:first-child {
    display: table-row;
}
form.cgkit-swatch-form table.variations tr.cgkit-hide-loop {
	display: none !important;
}
.cgkit-swatch-form summary {
	font-weight: bold;
    cursor: pointer;
    padding:  12px 15px 12px 15px;
    border-top: 1px solid #e2e2e2;
    font-size: 14px;
    cursor: pointer;
    position: relative;
    width: 100%;
}
.cgkit-swatch-form summary:before  {
	content: "";
	position: absolute;
	top:  50%;
	margin-top: -6px;
	width: 2px;
	height: 12px;
	right: 20px;
	background: #111;
	transition: transform 250ms ease 0s;
}
.cgkit-swatch-form summary:after {
	content: "";
	position: absolute;
	top:  50%;
	margin-top: -1px;
	width: 12px;
	height: 2px;
	right: 15px;
	background: #111;
	transition: transform 250ms ease 0s;
}
.cgkit-swatch-form summary::-webkit-details-marker {
	display: none;
}
.cgkit-swatch-form summary:focus {
    outline: 0;
}
.cgkit-swatch-form details[open] summary:before {
    transform: rotate(90deg);
}
.cgkit-swatch-form .blockUI {
    visibility: hidden;
}
ul.products li.product .cgkit-as-single-atc-wrap {
	position: relative;
	margin-top: 10px;
}
ul.products li.product .cgkit-as-single-atc-wrap .button {
	position: relative;
	width: 100%;
}
ul.products li.product.cgkit-swatch-hover .cgkit-as-empty-atc-wrap {
	display: none;
}
.cgkit-as-loop-atc-wrap {
	display: none;
}
ul.products li.product.cgkit-swatch-hover .cgkit-as-loop-atc-wrap {
	display: block;
}
.cgkit-attribute-swatches-wrap {
	padding: 0px;
	margin: 0px;
	border: 0px;
	box-shadow: none;
}
@media (max-width: 992px) {
	.cgkit-swatch-form details {
		overflow: hidden;
	}
	.cgkit-swatch-form .cgkit-swatch-quickadd {
		display: block;
	}
	.cgkit-swatch-form .cgkit-swatch-quickwrap {
		transition: height .5s ease;
		overflow: hidden;
	}
	.cgkit-swatch-form .cgkit-swatch-quickwrap:not(.active) {
		display: none;
	}
}
@media (min-width: 993px) {
	.cgkit-swatch-form summary {
		display: none;
	}
}
@media (max-width: 992px) {
	.ckit-attributes-wrap {
		padding:  0px 15px 15px 15px;
	}
	.cgkit-swatch-title {
		display: none;
	}
	form.variations_form.cgkit-swatch-form .cgkit-swatch-title {
		display: none;
	}
	form.cgkit-swatch-form .cgkit-as-swatches-clone {
		display: block;
		margin-top: 10px;
		margin-bottom: 10px;
	}
	form.cgkit-swatch-form .cgkit-as-swatches-original {
		display: none !important;
	}
	form.cgkit-swatch-form.cgkit-single-attribute details {
		display: none !important;
	}
	.cgkit-swatch-form details {
		margin-left: -15px;
		margin-bottom: -15px;
		width: calc(100% + 30px);
	}
	.col-full-nav .cgkit-swatch-form details {
		margin-left: 0;
		margin-right: 0;
		width: 100%;
	}
	.col-full-nav .cgkit-swatch-form summary,
	.col-full-nav .ckit-attributes-wrap {
		padding-left: 0;
		padding-right: 0;
	}
	.col-full-nav .cgkit-swatch-form summary:before {
		right: 5px;
	}
	.col-full-nav .cgkit-swatch-form summary:after {
		right: 0px;
	}
}
@media (min-width: 993px) {
	body ul.products li.product.cgkit-swatch-hover form.cgkit-swatch-form table.variations tr:first-child {
		position: relative;
		height: auto;
	}
	body ul.products li.product.cgkit-swatch-hover:hover form.cgkit-swatch-form table.variations tr,
	body ul.products li.product.cgkit-swatch-hover:focus-within form.cgkit-swatch-form table.variations tr {
    	display: table;
	}
	form.cgkit-swatch-form .single_variation_wrap {
		box-sizing: border-box;
		position: absolute;
		background: #fff;
		margin: 0px;
		text-align: center;
		width: 100%;
	}
	form.cgkit-swatch-form .single_variation_wrap p.out-of-stock {
		margin: 0px !important;
	}
	form.cgkit-swatch-form .cgkit-as-swatches-clone {
		display: none !important;
	}
	form.cgkit-swatch-form .cgkit-as-swatches-original {
		display: block;
	}
	body ul.products li.product.cgkit-swatch-hover:not(.product-category):before {
		-webkit-box-sizing: unset;
		box-sizing: unset; 
	}
}
@media (max-width: 770px) {
	.cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-button button {
	    width: auto;
    	height: 40px;
    	min-width: 40px;
    	line-height: 33px;
    	min-height: 34px;
    	font-size: 12px;
	}
	form.cgkit-swatch-form .cgkit-as-swatches-clone {
		margin-bottom: 5px;
	}
}
.rtl .cgkit-as-wrap .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-button {
	margin: 0px 0px 5px 5px;
}
.rtl .summary .cgkit-attribute-swatches .cgkit-attribute-swatch {
    margin: 0px 0px 8px 8px;
}
.rtl .cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-button {
	margin: 0px 0px 5px 5px;
}
.rtl .cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch {
	margin: 0 0px 5px 5px;
}
.rtl form.variations_form label .ckwtl-os-label-tip {
	right: 0px;
	left:  auto;
}

/* Shoptimizer only */
.product-align-center ul.products li.product .cgkit-swatch-form table * {
	text-align: center;
}
.product-align-center .cgkit-swatch-form .cgkit-as-wrap-plp .cgkit-attribute-swatches {
	margin: 0 0 0 5px; /* To offset the right-margin of 5px on each swatch */
}
@media (min-width: 993px) {
	/* If variations appear in the mega menu - display each row, no need to hover */
	.col-full-nav ul.products li.product.cgkit-swatch-hover form.cgkit-swatch-form table.variations tr {
		position: relative;
		display: table;
	}
	ul.products li.product.cgkit-swatch-hover form.cgkit-swatch-form table.variations tr {
		display: none;
	}
	ul.products li.product.cgkit-swatch-hover form.cgkit-swatch-form table.variations tr {
		position: absolute;
	}
}
@media (max-width: 992px) {
	.cgkit-swatch-form summary {
		font-size: 12px;
		border: none;
		margin: 0 15px 10px 15px;
    	text-decoration: underline;
    	text-decoration-thickness: 0.5px;
    	text-underline-offset: 0.16em;
		padding: 0;
    	padding-right: 17px;
    	display: inline-block;
		width: auto;
	}
	.cgkit-swatch-form summary:before {
   		margin-top: -5.5px;
    	width: 1px;
    	height: 9px;
    	right: 4.5px;
	}
	.cgkit-swatch-form summary:after {
    	margin-top: -1.5px;
    	width: 9px;
    	height: 1px;
    	right: 0.5px;
	}
	.m-grid-2 form.cgkit-swatch-form .cgkit-as-swatches-clone {
		margin-top: 0;
	}
	.cgkit-as-wrap-plp .cgkit-attribute-swatches:has(.cgkit-image),
	.cgkit-as-wrap-plp .cgkit-attribute-swatches:has(.cgkit-color) {
		width: 100%;
	}
}
@media (max-width: 770px) {
	.m-grid-2 .cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-image button {
		width: 32px;
		height: 32px;
	}
	.m-grid-2 .cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-color > button {
		width: 22px;
    	height: 22px;
	}
	.m-grid-2 .cgkit-as-wrap-plp .cgkit-attribute-swatches .cgkit-attribute-swatch.cgkit-color span.color-div {
		width: 18px;
    	height: 18px;
	}
    .m-grid-1 .cgkit-swatch-form summary {
    	font-size: 13px;
    	width: 100%;
    	margin: 0;
    	padding: 12px 15px;
    }
    .m-grid-1 .ckit-attributes-wrap {
    	padding: 0 15px 15px 15px;
    }
    .m-grid-1 .cgkit-swatch-form details {
    	margin-top: 15px;
    	border-top: 1px solid #eee;
    }
    .m-grid-1 .cgkit-swatch-form summary:before {
    	right: 20px;
    }
    .m-grid-1 .cgkit-swatch-form summary:after {
    	right: 16px;
    }
    .rtl.m-grid-1 .cgkit-swatch-form summary:before {
    	left: 20px;
    	right: auto;
    }
    .rtl.m-grid-1 .cgkit-swatch-form summary:after {
    	left: 16px;
    	right: auto;
    }
}
@-webkit-keyframes ckit-rotate {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg); } 
}
@keyframes ckit-rotate {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg); }
}