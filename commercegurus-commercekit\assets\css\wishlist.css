#commercekit-wishlist-popup{font-weight:700;line-height:50px;text-align:center;background:#fff;box-shadow:0 0 15px rgba(0,0,0,0.2);border-radius:6px;position:fixed;top:50%;left:50%;padding:7px 40px;z-index:10000;transform:translate(-50%,0)}.commercekit-wishlist.mini{position:absolute;right:15px;top:15px;z-index:2;width:30px;height:30px;background:#fff;border-radius:50%;opacity:0;transition:all .2s}@media (max-width:992px){.commercekit-wishlist.mini{opacity:1}}li.menu-item .commercekit-wishlist.mini{right:10px;top:10px}li.product .commercekit-wishlist a{width:30px;height:30px;position:absolute;top:0;z-index:1;display:block;text-align:center;height:100%;border-radius:99%;}.commercekit-wishlist.full{position:relative;margin-left:22px;line-height:1.5}.commercekit-wishlist.full a em.cg-wishlist-t:before,.commercekit-wishlist.full a em.cg-wishlist:before{margin-top:1px}.commercekit-wishlist a.processing:after,.commercekit-wishlist-table a.commercekit-remove-wishlist2.processing:after{position:absolute;top:3px;left:3px;-webkit-transition:opacity 0 ease;transition:opacity 0 ease;content:"";display:inline-block;width:22px;height:22px;border:1px solid rgba(0,0,0,.15);border-left-color:#fff;border-radius:50%;vertical-align:middle;-webkit-transition:opacity .25s ease;transition:opacity .25s ease;webkit-animation:ckit-rotate 450ms infinite linear;animation:ckit-rotate 450ms infinite linear;box-sizing:content-box}.commercekit-wishlist-table a.commercekit-remove-wishlist2.processing:after{top:-6px;left:-6px;width:30px;height:30px}@-webkit-keyframes ckit-rotate{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes ckit-rotate{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}.commercekit-wishlist a em{font-style:normal}.commercekit-wishlist a em.cg-wishlist-t:before,.commercekit-wishlist a em.cg-wishlist:before,.commercekit-wishlist.full a em.cg-wishlist-t:before{position:absolute;content:"";display:block;width:16px;height:16px;background:#333;-webkit-mask-position:center;-webkit-mask-repeat:no-repeat;-webkit-mask-size:contain}li .commercekit-wishlist a em.cg-wishlist-t:before,li .commercekit-wishlist a em.cg-wishlist:before{left:50%;top:50%;transform:translate(-50%,-50%)}.commercekit-wishlist a em.cg-wishlist-t:before,.commercekit-wishlist a em.cg-wishlist:before,.commercekit-wishlist.full a em.cg-wishlist-t:before{-webkit-mask-image:url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4.31802 6.31802C2.56066 8.07538 2.56066 10.9246 4.31802 12.682L12.0001 20.364L19.682 12.682C21.4393 10.9246 21.4393 8.07538 19.682 6.31802C17.9246 4.56066 15.0754 4.56066 13.318 6.31802L12.0001 7.63609L10.682 6.31802C8.92462 4.56066 6.07538 4.56066 4.31802 6.31802Z' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");mask-image:url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4.31802 6.31802C2.56066 8.07538 2.56066 10.9246 4.31802 12.682L12.0001 20.364L19.682 12.682C21.4393 10.9246 21.4393 8.07538 19.682 6.31802C17.9246 4.56066 15.0754 4.56066 13.318 6.31802L12.0001 7.63609L10.682 6.31802C8.92462 4.56066 6.07538 4.56066 4.31802 6.31802Z' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E")}.commercekit-wishlist a em.cg-wishlist-t:before{-webkit-mask-image:url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5 13L9 17L19 7' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");mask-image:url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5 13L9 17L19 7' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E")}.commercekit-wishlist.full a em.cg-wishlist, .commercekit-wishlist.full a em.cg-wishlist-t{position:absolute}ul.products li.product:hover .commercekit-wishlist.mini,ul.products li.product:focus-within .commercekit-wishlist.mini{opacity:1;transform:translateY(0px)}.commercekit-pages{clear:both;width:100%;text-align:right}.commercekit-wishlist-table tbody td,.commercekit-wishlist-table thead th{border-bottom:1px solid #eee;padding:12px 0;vertical-align:middle;font-size:14px}.commercekit-wishlist-table thead th{padding-top:0;border-bottom-width:2px; font-size:15px;}.commercekit-wishlist-table tbody td{padding-right:15px}.commercekit-wishlist-table tbody tr:last-child td{border:none}.commercekit-wishlist-table a{color:#000}.commercekit-wishlist-table .remove{vertical-align:middle;text-align:left;padding-left:0;padding-right:0;width:42px}.commercekit-wishlist-table .remove a{display:block;width:24px;height:24px;font-size:16px;line-height:19px;border-radius:100%;color:#ccc;font-weight:700;text-align:center;border:1.5px solid #ccc;transition:all .2s}.commercekit-wishlist-table .remove a:hover{color:#777;border-color:#777}.commercekit-wishlist-table .image-name{vertical-align:middle}.commercekit-wishlist-table .image-name .image{display:inline-block;vertical-align:middle;width:50px;margin-right:10px}.commercekit-wishlist-table .image{width:80px}.commercekit-wishlist-table .price del{color:#999}.commercekit-wishlist-table .stock .instock{color:#007f12; font-weight:bold;}.commercekit-wishlist-table .stock .outofstock{color:#999}.commercekit-wishlist-table .cart{text-align:right;padding-right:0;font-size:13px}.commercekit-wishlist-table .cart button,.commercekit-wishlist-table .cart a.button{padding: 0.5em 1em;line-height:1.5;transition:.2s all;border-radius:4px;font-size:13px;color:#fff;white-space:nowrap;font-weight:700;cursor:pointer;}.commercekit-wishlist-table{margin-top:10px;margin-bottom:2em}.commercekit-wishlist-table .center{text-align:center}.commercekit-wishlist-table a.commercekit-remove-wishlist2{width:24px;height:24px;display:inline-block;vertical-align:middle;text-align:center}.commercekit-wishlist-table a.commercekit-remove-wishlist2.processing{position:relative}.commercekit-wishlist a em.cg-wishlist-t:before,.commercekit-wishlist a em.cg-wishlist:before,.commercekit-wishlist.full a em.cg-wishlist-t:before{left:-22px}@media(max-width: 770px){.commercekit-wishlist-table{margin-bottom:inherit}.commercekit-wishlist-table thead{display:none}.commercekit-wishlist-table tbody td{display:flex;align-items:center;flex-direction:row;flex-wrap:wrap;padding:0 0 5px;font-size:13px;border:none}.commercekit-wishlist-table tbody td.name{padding-bottom:2px;font-size:14px;padding-right:40px;line-height:1.45}.commercekit-wishlist-table tbody td.stock{font-size:12px;opacity:.6}.commercekit-wishlist-table tr{position:relative;margin-bottom:15px;padding-bottom:15px;border-bottom:1px solid #e2e2e2;padding-left:120px;min-height:130px;display:block}.commercekit-wishlist-table tr:last-child{border:none}.commercekit-wishlist-table td.remove{position:absolute;top:0;right:0;z-index:1;width:inherit}.commercekit-wishlist-table td.image{position:absolute;top:5px;left:0;overflow:hidden;margin-bottom:0;padding-bottom:0;max-height:120px;border-bottom:none}.commercekit-wishlist-table .image{width:100px}.commercekit-wishlist-table .cart button{font-size:12px;padding:.5em 1.2em}}
.commercekit-wishlist-table .image img { display: block; } .commercekit-wishlist-pages { font-size: 14px; display: flex; justify-content: space-between; align-items: center; margin-top: -1em;margin-bottom: 4em; } .commercekit-wishlist-pages .tablenav-pages-navspan, .commercekit-wishlist-pages a { font-size: 18px; padding: 2px 2px;} .commercekit-wishlist-pages .paging-input { padding: 0 10px; } .commercekit-wishlist-pages a{ width: 34px;border: 1px solid #e2e2e2;height: 36px;align-items: center;display: inline-flex;justify-content: center;color: #111;border-radius:3px;box-shadow:0 4px 7px -2px rgba(0, 0, 0, 0.06) }.commercekit-wishlist-pages a:hover{border-color:#ccc;}.commercekit-wishlist-pages a:hover{color:#000}.commercekit-wishlist-table .image a{display:block}
.commercekit-wishlist a {display: inline-flex;}

/* RTL */
.rtl .commercekit-wishlist {margin-right: 22px;margin-left: 0;}
.rtl .commercekit-wishlist.full a em.cg-wishlist-t:before,
.rtl .commercekit-wishlist.full a em.cg-wishlist:before,
.rtl .commercekit-wishlist.full a em.cg-wishlist-t:before {left: auto;right: -22px;}
.commercekit-wishlist a i.cg-wishlist {right: 0px;left: auto;}

.rtl .menu-item .commercekit-wishlist.mini {left: 10px;}
.rtl .commercekit-wishlist-table .cart {text-align: left;padding-left: 0;}
.rtl .commercekit-wishlist-table tbody td {padding-left: 15px;padding-right: 0;}
@media screen and (max-width: 770px) {
	.rtl .commercekit-wishlist-table .cart, .rtl .commercekit-wishlist-table .remove {text-align: right;}
}
/* Shoptimizer */
.theme-shoptimizer .commercekit-wishlist.full {margin-bottom: 15px;}
.theme-shoptimizer .commercekit-wishlist a {font-size:13px;font-weight: 600;color:#111;}
.theme-shoptimizer .commercekit-wishlist a:hover{color:#111;}
.theme-shoptimizer .commercekit-wishlist a:hover span {text-decoration: underline;text-decoration-thickness: 0.5px;text-underline-offset: 0.18em;}
@media screen and (max-width: 600px) {.theme-shoptimizer.m-grid-2 .commercekit-wishlist.mini {top: 10px;right: 10px;}}
.rtl.theme-shoptimizer .commercekit-wishlist.mini {left: 10px;right:auto;}
@media screen and (min-width: 993px) {
	.theme-shoptimizer .commercekit-wishlist.mini {right: 30px;}
	.rtl.theme-shoptimizer .commercekit-wishlist.mini {left: 30px;right:auto}
	.rtl li.menu-item .commercekit-wishlist.mini {left:10px;right:auto;}
	ul.commercekit-wishlist-list.products li.product { width: 25%; }
}
body:has(.wsl-no-title) h1.entry-title { display: none; }
@media (max-width: 992px) {
    ul.products:has(.wsl-no-products) {
    	grid-template-columns: repeat(1, minmax(10px, 1fr));
   }
}
ul.commercekit-wishlist-list.products .wsl-no-products { width: 100%; list-style: none; min-height: 450px; text-align: center; padding-bottom: 4rem; }
ul.commercekit-wishlist-list.products .wsl-no-products .wsl-no-icon { padding: 0; margin: 0; }
.wsl-no-icon svg { width: 200px; }
ul.commercekit-wishlist-list.products .wsl-no-products .wsl-no-title { font-size: clamp(1.625rem, 0.2276rem + 4.4715vw, 3rem); /* 26-48 */ color: #242424; font-weight: 600; margin-top: 0px; margin-bottom: 0.5rem; }
ul.commercekit-wishlist-list.products .wsl-no-products .wsl-no-desc { font-size: clamp(0.9375rem, 0.747rem + 0.6098vw, 1.125rem); /* 15-18 */ color: #444; margin-bottom: 1.5rem; }
ul.commercekit-wishlist-list.products .wsl-no-products .wsl-no-shop { padding-left:2rem; padding-right:2rem; }
a.commercekit-remove-wishlist2.wsl-remove { font-size: 0px; }
a.commercekit-remove-wishlist2.wsl-remove:before { -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round' class='feather feather-trash-2'%3E%3Cpolyline points='3 6 5 6 21 6'%3E%3C/polyline%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'%3E%3C/path%3E%3Cline x1='10' y1='11' x2='10' y2='17'%3E%3C/line%3E%3Cline x1='14' y1='11' x2='14' y2='17'%3E%3C/line%3E%3C/svg%3E"); mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round' class='feather feather-trash-2'%3E%3Cpolyline points='3 6 5 6 21 6'%3E%3C/polyline%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'%3E%3C/path%3E%3Cline x1='10' y1='11' x2='10' y2='17'%3E%3C/line%3E%3Cline x1='14' y1='11' x2='14' y2='17'%3E%3C/line%3E%3C/svg%3E"); position: absolute; content: ""; display: block; width: 16px; height: 16px; background: #333; -webkit-mask-position: center; -webkit-mask-repeat: no-repeat; -webkit-mask-size: contain; left: 50%; top: 50%; transform: translate(-50%, -50%); }