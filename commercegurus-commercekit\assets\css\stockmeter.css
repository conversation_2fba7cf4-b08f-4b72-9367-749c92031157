/* Stock Meter CSS */
.commercekit-inventory { display: inline-block; width: 45%; margin-bottom: 15px; vertical-align: top; line-height: 1.25; position: relative; }
.commercekit-inventory.cgkit-inventory-shortcode { width: 100%; }
.commercekit-inventory span { font-size: 13px; }
.commercekit-inventory .progress-bar { float: none; position: relative; width: 100%; height: 10px; margin-top: 10px; padding: 0; border-radius: 5px; background-color: #e2e2e2; transition: all 0.4s ease; }
.commercekit-inventory .progress-bar span { position: absolute; top: 0; left: auto; width: 28%; height: 100%; border-radius: inherit; background: #f5b64c; transition: width 3s ease; }
.commercekit-inventory .progress-bar.full-bar span { width: 100% !important; }
.commercekit-inventory .cki-variation { width: 100%; }
@media (max-width: 500px) { .commercekit-inventory { display: block; margin-top: 20px; width: 100%; border: none; } 
.commercekit-inventory .cki-variation { position: relative; } }