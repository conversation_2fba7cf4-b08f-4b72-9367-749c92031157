/* CSS Document */
.toplevel_page_commercekit .notice,
.toplevel_page_commercekit .updated {
    margin: 15px 20px 15px 20px;
}
body.toplevel_page_commercekit #wpbody-content {
	padding-bottom: 0;
}
.ckit-admin-wrapper {
	padding: 30px 0 80px 0;
	background-color: #f4f4f4;
}
.form-table th {
    font-size: 14px;
    width: 25%;
}
.wp-core-ui select:hover {
	color: inherit;
}
.toggle-switch {
	position: relative;
	display: inline-block;
	width: 54px;
	height: 26px;
	margin-right: 10px;
}
.toggle-switch input {
	opacity: 0;
	width: 0;
	height: 0;
}
.toggle-slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	-webkit-transition: .4s;
	transition: .4s;
	border-radius: 34px;
}
.toggle-slider:before {
	position: absolute;
	content: "";
	height: 20px;
	width: 20px;
	left: 4px;
	bottom: 3px;
	background-color: white;
	-webkit-transition: .4s;
	transition: .4s;
	border-radius: 50%;
}
.postbox .handlediv {
    float: right;
    width: 46px;
    height: 36px;
    margin: 0;
    margin-right: 0px;
    padding: 0;
    border: 0;
    background: 0 0;
    cursor: pointer;
}
input:checked + .toggle-slider {
	background-color: #2196F3;
}
input:focus + .toggle-slider {
	box-shadow: 0 0 1px #2196F3;
}
input:checked + .toggle-slider:before {
	-webkit-transform: translateX(26px);
	-ms-transform: translateX(26px);
	transform: translateX(26px);
}
input[type=radio].pdt-type {
	margin-right: 5px;
	position: relative;
	margin-top: -2px;
}
span.radio-space {
	display: inline-block;
	width: 50px;
}
#settings-content {
	width: calc(100% - 255px);
	display: inline-block;
}
#settings-content .dashboard h2 {
	display: flex;
	justify-content: space-between;
}
#settings-note {
	width: 180px;
	min-width: 180px;
	display: inline-block;
	margin-left: 15px;
	vertical-align: top;
	padding: 0px 25px 10px 25px;
	border-radius: 6px;
}
#settings-note h4 {
	font-size: 17px;
	line-height: 1.3;
	margin-bottom: 0;
	color: #111;
}
#settings-note p {
	font-size: 13px;
	line-height: 1.4;
	color: #666;
	letter-spacing: 0.008em;
}
#settings-note img {
	max-width: 200px;
}
#settings-note p strong {
	color: #222;
	font-weight: 500;
	font-size: 14px;
}
#settings-note .child-message {
	display: block;
	border-top: 1px solid #eee;
	padding-top: 10px;
	margin-top: 15px;
	font-size: 13px;
	color: #323232;
}
#checkout-countdown {
	width: 100%;
	display: inline-block;
}
#product-countdown,
#product-badge {
	margin-bottom: 0px;
	padding-bottom: 0px;
}
.postbox {
	border-color: #e2e2e2;
	box-shadow: 0 1px 4px rgba(0,0,0,0.05);
	margin-bottom: 20px;
	border-radius: 6px;
}
.content-box h2 {
	border-bottom: 1px solid #f1f1f1;
	padding: 20px 30px;
	margin: 0px;
	font-size: 20px;
	font-weight: 500;
}
.content-box h2.nested-heading {
	border-top: 1px solid #f1f1f1;
	border-bottom: 0;
	padding-top: 30px;
	padding-bottom: 0;
	margin-bottom: -5px;
}
.content-box h2.order-bump-statistics--heading {
	padding-top: 14px;
	padding-bottom: 14px;
}
.content-box h2.gray {
	border: 0px !important;
	cursor: move;
	padding: 13px 22px;
	font-size: 16px;
	font-weight: 500;
	color: #444;
}
.content-box h2.has-subtabs {
	border: none;
}
.ends-wrap label {
	width: 100px;
	display: inline-block;
	text-align: center;
}
.ends-wrap input.ends {
	width: 95px;
	display: inline-block;
}
input.title,
table input[type="text"] {
	width: 100%;
}
input.title,
table input[type="text"],
table input[type="number"],
textarea {
	padding: 4px 12px;
	border: 1px solid #bfbebe;
	box-shadow: 0 1px 0 rgba(27,31,35,.04),inset 0 1px 0 hsla(0,0%,100%,.25);
}
input#product_id {
	min-width: 150px;
}
.wp-core-ui table select {
	padding-left: 12px;
	padding-top: 4px;
	padding-bottom: 4px;
	border: 1px solid #bfbebe;
	box-shadow: 0 1px 0 rgba(27,31,35,.04),inset 0 1px 0 hsla(0,0%,100%,.25);
	width: 100%;
	border-radius: 4px;
}
button.handlediv {
	height: 48px !important;
}
.postbox .handlediv:focus {
	border-radius: 3px;
}
.wp-core-ui .button-secondary {
	padding: 2px 12px;
}
.button-secondary .dashicons {
	margin-top: 8px;
	margin-left: -6px;
	font-size: 15px;
}
.wp-core-ui .button-primary.clear-cache {
	margin-left: 50px;
	vertical-align: middle;
}
.setting-page-title {
	margin-top: 36px;
	color: #111;
	font-size: 38px;
	font-weight: 600;
	-webkit-font-smoothing: antialiased;
}
.wrap {
	margin: 0;
}
#wpcontent {
	padding-left: 0;
}
.ckit-admin-wrapper {
	padding-left: 20px;
	padding-right: 20px;
}
.commercekit-admin-header {
	box-sizing: border-box;
  background: #fff;
  padding: 25px 25px 25px 25px;
}
.commercekit-logo--wrapper {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-wrap: wrap;
}
.commercekit-logo {
	height: 50px;
	margin-right: 1rem;
}
p.intro {
	margin-top: 10px;
	margin-bottom: 0px;
  font-size: clamp(0.8125rem, 0.6855rem + 0.4065vw, 0.9375rem); /* 13-15 */
  font-weight: 400;
  color: #555;
  letter-spacing: 0.005em;
  -webkit-font-smoothing: antialiased;
}
p.intro a {
	text-decoration-thickness: 1px;
  text-underline-offset: 0.12em;
}
.commercekit-admin-header--links div,
.commercekit-admin-header--links a {
	margin-right: 0.85rem;
	font-size: 13px;
	line-height: 2;
}
.commercekit-admin-header--links div {
	color: #666;
	padding-right: 0.75rem;
	border-right: 1px solid #e2e2e2;
}
.commercekit-admin-header--links a {
	text-decoration-thickness: 1px;
  text-underline-offset: 0.12em;
}
.commercekit-admin-header--links nav {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	row-gap: 10px;
}
/* Wrapper */
.ckit-admin-wrapper {
	display: flex;
	align-items: flex-start;
	-webkit-font-smoothing: antialiased;
}
/* Tabs */
.ckit-admin-wrapper .nav-tab-wrapper {
	background: #212838;
	border: none;
	border-radius: 6px;
	padding: 12px 12px 8px 12px;
	-webkit-font-smoothing: antialiased;
	width: 170px;
	min-width: 170px;
	margin-right: 20px;
	position: sticky;
	top: 50px;
}
.ckit-admin-wrapper .nav-tab-wrapper a {
	display: block;
	text-decoration: none;
	color: #ccc;
	font-size: 14px;
	font-weight: 400;
	padding: 9px 13px;
	margin-bottom: 4px;
	transition: 0.2s all;
}
.nav-tab-wrapper .nav-item-support {
	position: relative;
	margin-top: 14px;
}
.nav-tab-wrapper .nav-item-support:before {
	width: calc(100% + 24px);
	height: 1px;
	content: "";
	background: #555;
	position: absolute;
	top: -8px;
	left: -12px;
}
.nav-tab-wrapper a
.nav-tab-wrapper a:hover,
.nav-tab-wrapper a.nav-item-active {
	color: #fff;
	background: #121826;
	border-radius: 6px;
}
.nav-tab-wrapper a.nav-item-active {
	font-weight: 700;
}
/* Connection status */
.ckit-connection-status {
	display: flex;
	align-items: center;
	text-transform: uppercase;
	font-size: 12px;
	letter-spacing: 0.5px;
	font-weight: 500;
	margin-top: 6px;
	margin-bottom: -5px;
}
.ckit-connection-status.connected {
	color: #46b450;
}
.ckit-connection-status.not-connected {
	color: #d80303;
}
.ckit-connection-status svg {
	width: 20px;
	height: 20px;
	margin-right: 6px;
}
/* Dashboard grid */
.ckit-features-grid {
	margin: 30px 0px 10px 0px;
	display: grid;
	grid-template-columns: repeat(2, minmax(0, 1fr));
	gap: 2rem;
}
.ckit-features-grid section {
	border: 1px solid #e2e2e2;
	padding: 20px 20px 20px 20px;
	box-shadow: 0 1px 10px rgb(0 0 0 / 8%);
	border-radius: 6px;
	position: relative;
	display: flex;
	flex-direction: column;
}
.ckit-features-grid section .status .active,
.ckit-features-grid section .status .inactive {
	position: absolute;
	top: -10px;
	left: 18px;
	border-radius: 99%;
	border: 2px solid #fff;
	text-transform: uppercase;
	font-size: 9px;
	font-weight: 600;
	padding: 3px 10px;
	border-radius: 20px;
	display: flex;
	align-items: center; 
}
.ckit-features-grid section .status .active {
	background: #267ffd;
	color: #fff;
	display: none;
}
.ckit-features-grid section.active .status .active {
	display: flex;
}
.ckit-features-grid section.active .status .active.shortcode {
	background: #e03e02;
	color: #fff;
	margin-left: 70px;
}
.ckit-features-grid section .status .inactive {
	background: #f2f2f2;
	color: #888;
}
.ckit-features-grid section .active svg {
	width: 9px;
	height: 9px;
	color: #fff;
	margin-right: 2px;
}
.ckit-features-grid section:not(.active) .activated {
	display: none;
}
.ckit-features-grid section.active {
	border-width: 1px;
	border-color: #9cccf9;
	box-shadow: 0 1px 10px rgb(1 88 168 / 16%);
}
.ckit-features-grid section h3 {
	font-size: 18px;
	font-weight: 500;
	margin: 0px;
}
.ckit-features-grid section h3 a {
	text-decoration: none;
}
.ckit-features-grid section:not(.active) h3 a,
.ckit-features-grid section:not(.active) h3 a:hover {
	color: #323232;
}
.ckit-features-grid section p {
	margin-top: 0;
	font-size: 14px;
	color: #565656;
}
.ckit-feature-heading {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 0 0 10px 0;
	border-bottom: 1px solid #eee;
	padding-bottom: 10px;
}
.ckit-features-grid section.active span.inactive,
.ckit-features-grid section.active .status .inactive {
	display: none;
}
.ckit-feature-content {
	display: flex;
	flex-direction: column;
	flex-grow: 1;
}
.ckit-perf {
	display: flex;
	margin-top: auto;
	color: #666;
}
.ckit-perf strong {
	color: #111;
	padding-right: 6px;
	margin-right: 4px;
	border-right: 1px solid #e2e2e2;
}
.ckit-perf svg {
	width: 16px;
	height: 16px;
	color: #999;
	margin-right: 6px;
	flex-shrink: 0;
	margin-top: 2px;
}
/* Headings */
h1, h2, h3, h4, h5, h6 {
	font-weight: 500;
}
/* Forms */
.form-table td {
	font-size: 14px;
	padding: 10px 10px;
}
.form-table.admin-support td,
.form-table td:has(.top-divider),
.form-table td:has(#cgkit-ajs-cache-status) {
	padding-left: 0;
	padding-right: 0;
}
.form-table th {
	padding: 10px 10px 10px 0;
	vertical-align: middle;
	font-weight: 500;
}
.form-table small {
	color: #666;
	display: block;
	margin-top: 5px;
}
.pc100 {
	width: 100%;
}
.ckit-pt5 {
	padding-top: 2px;
}
input, textarea {
	font-size: 14px;
}
select.conditions {
	width: 100%;
}
span.ends-label {
	font-size: 11px;
	color: #888;
}
h3.ckit_sub-heading {
	padding-top:  20px;
	border-top: 1px solid #e2e2e2;
}
.w-120 {
	display: inline-flex;
	min-width: 120px;
	align-items: center;
}
.w-150 {
	display: inline-flex;
	min-width: 150px;
	align-items: center;
}
.form-table .w-120 input[type=radio],
.form-table .w-150 input[type=radio] {
	margin: 0 8px 0 0;
}
.submit-button .button-primary {
	font-size: 15px;
	padding: 5px 30px;
	font-weight: 500;
	height: 46px;
	line-height: inherit;
}
.add-new-countdown .button-primary {
	background: #FFF !important;
	color: #000 !important;
	border: 2px solid #CCC !important;
	border-radius: 0px;
	padding: 5px 10px 5px;
	font-size: 18px;
	box-shadow: none !important;
}
.add-new-countdown .button-primary span {
	border: 1px solid #000;
	border-radius: 50%;
	padding: 3px 3px 1px;
	margin-right: 5px;
	margin-top: 7px;
}
#commercekit-form {
	position: relative;
}
#commercekit-form #ajax-loading-mask {
	position: fixed;
	width: 100%;
	height: 100%;
	background: #FFF;
	opacity: 0.7;
	display: none;
	z-index: 99;
	align-items: center;
	justify-content: center;
}
#commercekit-form #ajax-loading-mask[style*='display: block']{
    display: flex !important;
}
#commercekit-form #ajax-loading-mask .ajax-loading {
	width: 45px;
	height: 45px;
	margin-top: -45px;
	margin-left: -45px;
	background:  url(../images/spinner.svg) center center no-repeat;
	visibility: visible;
	background-size: cover;
}
.delete-ckit--element {
	display: inline-flex;
	align-items: center;
}
.delete-ckit--element svg {
	width: 16px;
	margin-right: 0.4rem;
	stroke: #444;
}
.tab-content .delete-countdown,
.tab-content .delete-orderbump,
.tab-content .delete-badge {
	color: #B60000;
	font-weight: 400;
	font-size: 13px;
	text-decoration-thickness: 1px;
  text-underline-offset: 0.14em;
}
.tab-content .delete-countdown:hover,
.tab-content .delete-orderbump:hover,
.tab-content .delete-badge:hover {
	color: #c10b0b;
}
.extra-notes {
	float: right;
	width: calc(100% - 250px);
	font-size: 18px;
	line-height: 25px;
}
.extra-notes h4 {
	margin-top: 10px;
	margin-bottom: 10px;
}
.extra-notes ul {
	margin: 0px;
	padding-left: 20px;
}
.extra-notes ul li {
	list-style: disc;
}
#commercekit_wsl_page {
	width: 100%;
	max-width: 80%;
	margin-bottom: 10px;
}
.admin-wishlist input {
	width: 80%;
}
#settings-content.dashboard h2 {
	display: flex;
	justify-content: space-between;
}
.postbox .inside {
	padding-left: 30px;
	padding-right: 30px;
	margin: 20px 0;
}
.postbox .inside.add-new-order-bump {
	margin-top: 0;
}
.postbox .inside.meta-box-sortables {
	margin-bottom: 0;
}
.postbox .inside.meta-box-sortables .inside {
	padding-left: 22px;
	padding-right: 22px;
}
.content-box h2 span.table-heading {
	font-weight: 500;
	font-size: clamp(1.125rem, 0.998rem + 0.4065vw, 1.25rem); /* 18-20 */
}
table.admin-dashboard th {
	width: 70px;
	text-align: right;
	padding-left: 0px;
	padding-right: 0px;
}
table.admin-dashboard h4 {
	margin: 0px;
	padding: 0px;
	color: #0073aa;
	font-weight: 600;
	font-size: 17px;
}
table.admin-dashboard h4 a {
	text-decoration: none;
}
.postbox.closed {
	border-bottom: 1px solid #ccd0d4;
}
.form-table td p {
	font-size: 14px;
	margin-top: 0px;
}
table.admin-dashboard.form-table td.right {
	font-size: 13px;
	color: #666;
	line-height: 1.3;
}
table.admin-dashboard.form-table td.right strong {
	color: #444;
	font-size: 12px;
}
table.admin-support h4 {
	margin: 0px;
	padding: 0px;
	font-size: 16px;
	font-weight: 600;
}
table.admin-support p {
	margin-bottom: 7px;
	margin-top: 2px;
	font-size: 14px;
	color: #666;
}
span.star {
	color: #B60000;
	font-size: 16px;
}
table .left {
	text-align: left;
}
table .right {
	text-align: right;
}
table .center {
	text-align: center;
}
table.admin-order-bump select.error,
table.admin-order-bump span.error,
table.admin-order-bump input.error,
table.countdown-timer input.error,
table.product-gallery input.error,
table.product-badge input.error {
	border: 2px solid #f00;
	box-shadow: none;
}
table.admin-order-bump .input-error,
table.countdown-timer .input-error,
table.product-gallery .input-error,
table.product-badge .input-error {
	color: #B60000;
	font-size: 12px;
	margin-top: 4px;
}
table.admin-support input.input {
	width: 80%;
	padding: 5px 10px;
	border-color: #ccc;
}
table.admin-support input.input:focus,
table.admin-support textarea:focus {
	outline: none;
	box-shadow: 0 0 0 3px rgba(164,202,254,.45);
	border-color: #a4cafe !important;
}
table.admin-support textarea.input {
	width: 100%;
	min-height: 200px;
	padding: 10px;
	border-color: #ccc;
}
.wp-core-ui table.admin-support .button-primary {
	font-size: 16px;
	padding: 5px 30px;
	font-weight: 400;
	height: 46px;
}
.cg-notice-success {
	box-shadow: 0 1px 1px rgba(0,0,0,.04);
	padding: 12px 15px;
	border: 1px solid #72d07a;
	border-radius: 6px;
	margin: 20px 0px;
	background: #f1faf2;
}
.cg-notice-success:last-child {
	margin-bottom: 0;
}
.cg-notice-success p,
.form-table td .cg-notice-success p {
	font-size: 13px;
	margin: 0;
	color: #046e0d;
}
table.admin-dashboard span.inactive {
	background: #F7F7F7;
  color: #888888;
  text-transform: uppercase;
  font-size: 10px;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 20px;
  margin-left: 10px;
  top: -3px;
  position: relative;
}
table.admin-dashboard tr.active span.inactive {
	display: none;
}
.dashicons-info,
.tooltip-wrapper {
	position: relative;
	display: inline-flex;
	margin-left: 0.25rem;
}
.label-tooltip {
	display: inline-flex;
	align-items: center;
}
.tooltip {
 	position:relative; 
	width: 20px;
	height: 20px;
	position: absolute;
	left: 0px;
	top: 0;
}
.tooltip:before {
  content: attr(data-text); /* here's the magic */
  position:absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 11.5px;
  font-weight: 400;
  line-height: 1.35em;
  /* move to right */
  left: 30px;
  margin-left: 0px; /* and add a small left margin */
  z-index: 10;
  /* basic styles */
  width: 200px;
  padding: 10px 15px;
  border-radius: 4px;
  background: #000;
  color: #fff;
  text-align: center;
  display: none; /* hide by default */
}
.tooltip:hover:before {
  display: block;
}
.tooltip:after {
  content: "";
  position: absolute;
  /* position tooltip correctly */
  left: 100%;
  margin-left: -5px;
  /* vertically center */
  top: 50%;
  transform: translateY(-50%);
  /* the arrow */
  border: 10px solid #000;
  border-color: transparent black transparent transparent;
  display: none;
}
.tooltip:hover:before,
.tooltip:hover:after {
  display: block;
}
.tooltip-icon {
	width: 18px;
	stroke: #888;
}
#ctd-order-notice {
	color: #666;
	font-size: 13px;
	padding-top: 10px;
	width: 100%;
	font-weight: 400;
	line-height: 1.5;
}
.form-warning {
	clear: both;
	color: red;
	font-weight: bold;
	padding: 10px 0px;
}
ul.subtabs {
	display: flex;
	width: 100%;
	box-sizing: border-box;
	margin-top: 0;
	margin-bottom: 30px;
	padding: 0px 30px;
	list-style: none;
	font-size: 14px;
	border-bottom: 1px solid #e2e2e2;
}
ul.subtabs li {
	margin: 0;
}
ul.subtabs li:last-child {
	border:  none;
}
ul.subtabs li a {
	text-decoration: none;
	padding: 0.7rem 1.25rem;
	border: 1px solid #e2e2e2;
	border-bottom: none;
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
	display: inline-flex;
	margin-right: 0.5rem;
	position: relative;
	bottom: -1px;
	background: #f8f8f8;
	color: #555;
}
ul.subtabs li a.active {
	color: #000;
	font-weight: 500;
	border-bottom: 1px solid #fff;
	background: #fff;
}
.wp-core-ui .button#reset-obp-statistics {
	font-size: 10px;
	text-transform: uppercase;
}
ul.order-bump-statistics {
	width: calc(100% + 20px);
  padding: 0px;
  margin: 0px -10px -15px -10px;
}
ul.order-bump-statistics li {
	width: calc(20% - 25px);
	display: inline-block;
	border-right: 2px solid #f1f1f1;
	padding: 10px;
	vertical-align: text-top;
}
ul.order-bump-statistics li:last-child {
	border-right: 0px;
}
ul.order-bump-statistics li .title {
	font-size: 12px;
	text-transform: uppercase;
	letter-spacing: 0.03em;
	color: #666;
}
ul.order-bump-statistics li .text-large {
	font-size: 26px;
	font-weight: 600;
}
ul.order-bump-statistics li .text-small {
	font-size: 15px;
	margin-bottom: 5px;
	font-weight: 400;
	color: #4f626c;
	margin-top: 6px;
}
ul.order-bump-statistics li .progress-bar {
	width: 100%;
	height: 6px;
	border-radius: 3px;
	background-color: #f1f1f1;
	position: relative;
}
ul.order-bump-statistics li .progress-bar span {
	height: 6px;
	border-radius: 3px;
	background-color: #46b450;
	position: absolute;
}
ul.waitlist-statistics,
ul.wishlist-statistics {
    width: calc(100% + 20px);
    padding: 0px;
    margin: 0px -10px -15px -10px;
}
ul.waitlist-statistics li,
ul.wishlist-statistics li {
	width: calc(25% - 25px);
	display: inline-block;
	border-right: 2px solid #f1f1f1;
	padding: 10px;
	vertical-align: text-top;
}
ul.waitlist-statistics li:last-child,
ul.wishlist-statistics li:last-child {
	border-right: 0px;
}
ul.waitlist-statistics li .title,
ul.wishlist-statistics li .title {
	font-size: 12px;
	text-transform: uppercase;
	letter-spacing: 0.03em;
	color: #666;
}
ul.waitlist-statistics li .text-large,
ul.wishlist-statistics li .text-large {
	font-size: 26px;
	font-weight: 600;
}
ul.waitlist-statistics li .text-small,
ul.wishlist-statistics li .text-small {
	font-size: 15px;
	margin-bottom: 5px;
	font-weight: 400;
	color: #4f626c;
	margin-top: 6px;
}
ul.waitlist-statistics li .progress-bar,
ul.wishlist-statistics li .progress-bar {
	width: 100%;
	height: 6px;
	border-radius: 3px;
	background-color: #f1f1f1;
	position: relative;
}
ul.waitlist-statistics li .progress-bar span,
ul.wishlist-statistics li .progress-bar span {
	height: 6px;
	border-radius: 3px;
	background-color: #46b450;
	position: absolute;
}
/* Top divider */
.top-divider {
	border-top: 1px solid #e2e2e2;
	padding-top: 1.5rem;
	margin-top: 1rem;
	margin-bottom: 0;
}
/* Beta module */
.beta-module {
	font-size: 10px;
  position: relative;
  color: #111;
  background: #fdc900;
  padding: 3px 10px 3px 10px;
  border-radius: 20px;
  display: inline-block;
  top: -2px;
  margin-left: 4px;
  font-weight: 700;
  text-transform: uppercase;
  height: 12px;
  line-height: 12px;
}
.explainer {
	border: 1px solid #e2e2e2;
	padding: 20px 30px;
	margin: 20px 0px;
	border-radius: 0px;
	box-shadow: 0 1px 10px rgb(0 0 0 / 3%);
}
.explainer h3 {
	margin-top: 0.75em;
	font-size: 18px;
	font-weight: 500;
	margin-bottom: 10px;
}
h3.mb0 {
	margin-top: 1.25em;
	margin-bottom: -10px;
}
.explainer p {
	font-size: 13px;
  line-height: 1.45;
  color: rgb(71 85 105);
  letter-spacing: 0.008em;
}
.explainer a {
	font-weight: 500;
}
.mini-explainer {
	margin: 20px 0 10px 0;
	padding: 12px 15px;
	border-radius: 6px;
	font-size: 13px;
	line-height: 1.4;
	background: #fcf9ef;
	border: 1px solid #fcc577;
	color: #111;
}
.mini-explainer a {
	color: #ab3f09;
	font-weight: 500;
}
.mini-explainer > p {
	margin-top: 0;
}
.mini-explainer p,
.form-table .mini-explainer p {
	font-size: 13px;
	line-height: 1.43;
	letter-spacing: 0.008em;
	margin-bottom: 10px;
}
.mini-explainer p:last-child,
.form-table .mini-explainer p:last-child {
	margin-bottom: 0;
}
.mini-explainer.cgkit-shortcode-help {
	position: relative;
	padding-left: 50px;
}
.mini-explainer.cgkit-shortcode-help svg {
	position: absolute;
	top: 14px;
	left: 15px;
	width: 20px;
	height: 20px;
	stroke: #e99235;
}
/* Heading */
h2.heading {
	font-size: 32px;
	margin: 0.5em 0;
}
/* Logs */
.log-message {
	border: 1px solid #e2e2e2;
	padding: 12px 12px;
	margin-bottom: 10px;
	border-radius: 3px;
}
.log-message.log-failed {
	background: #ffe7e7;
	border-color: #ffe7e7;
	color: #9b0e0e;
	padding: 12px 12px;
	margin-bottom: 10px;
	border-radius: 3px;
	font-size: 13px;
}
.log-message.log-failed a {
	color: #570202;
}
.cache-loader {
	display: flex;
	justify-content: center;
	align-items: center;
	background: #f6f6f6;
	padding: 12px 10px;
	margin: 10px 0 20px 0;
	border-radius: 3px;
}
.cache-bar {
	background: #f6f6f6;
	height: 16px;
	border-radius: 3px;
	margin-bottom: 10px;
}
.cache-progress,
.cache-completed {
	background-color: #2196F3;
	height: 16px;
	border-radius: 3px;
}
.cache-progress {
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
}
.cache-value {
	display: flex;
	justify-content: center;
	align-items: center;
	font-weight: 500;
}
.cache-value.completed {
	color: #438a14;
}
.cache-value.completed svg {
	width: 18px;
	height: 18px;
	margin-right: 6px;
	color: #52a819;
}
.cache-processing {
	margin-bottom: 20px;
}
.att-loader,
.att-loader:after {
  border-radius: 50%;
  width: 12px;
  height: 12px;
  margin-right: 8px;
}
.att-loader {
  font-size: 10px;
  position: relative;
  text-indent: -9999em;
  border-top: 2px solid rgba(196,196,196, 0.5);
  border-right: 2px solid rgba(196,196,196, 0.5);
  border-bottom: 2px solid rgba(196,196,196, 0.5);
  border-left: 2px solid #999;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation: load8 1.1s infinite linear;
  animation: load8 1.1s infinite linear;
}
@-webkit-keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
/* Fast Search Results */
.cg-fast-search {
	position: relative;
	margin: 20px 0px 0 0px;
	padding: 15px 25px;
	background: #fcf9ef;
  border: 1px solid #fcc577;
  border-radius: 4px;
}
.cg-fast-search td {
	padding-left: 0;
	padding-right: 0;
	display: table-cell;
}
.form-table td.cg-fast-search-wrap,
.cg-fast-search td:has(.toggle-switch) {
	display: table-cell;
}
.cg-fast-search svg {
	width: 50px;
	position: absolute;
  top: -18px;
  right: 20px;
}
.form-table td .cg-fast-search p {
	font-size: 13px;
	margin-top: 5px;
	color: #444;
}
/* Seclect2 */
.form-table .select2-container {
	min-width: 100%;
	max-width: 100%;
	margin-bottom: 5px;
}
.form-table label:has(select) {
	display: inline-block;
	width: 100%;
}
/* Ajax Search Reports */
.postbox .ajax-search-reports .ajax-search-reports-box h2 {
	border: 0px;
	font-size: 16px;
	font-weight: 400;
	margin: 0;
	padding: 0;
}
.postbox .ajax-search-reports h2 {
	margin-top: 20px;
	font-weight: 500;
	border: none;
}
.postbox .ajax-search-reports p {
	font-size: 13px;
	line-height: 1.3;
	margin-top: 0;
	margin-bottom: 0;
}
.ajax-search-reports .ajax-search-reports-box h3 {
	margin: 0 0 5px 0;
	font-size: 36px;
	font-weight: 800;
}
.postbox .ajax-search-reports {
	padding: 10px 30px;
}
.ajax-search-reports-boxes {
	display: grid;
 	grid-template-columns: repeat(3, minmax(0, 1fr));
 	column-gap: 20px;
}
.ajax-search-reports .ajax-search-reports-box {
	padding: 22px;
	border: 1px solid #e2e2e2;
	box-shadow: 0 1px 4px rgba(0,0,0,0.05);
}
table.ajax-search-reports-list {
	width: 100%;
	margin-top: 15px;
	margin-bottom: 15px;
	border-collapse: collapse;
}
table.ajax-search-reports-list th,
table.ajax-search-reports-list td {
	padding: 8px 0;
	border-bottom: 1px solid #e2e2e2;
	font-size: 14px;
}
table.ajax-search-reports-list td a {
	text-decoration-thickness: 1px;
  text-underline-offset: 0.14em;
}
table.ajax-search-reports-list th {
	font-weight: 500;
}
table.ajax-search-reports-list th span,
table.ajax-search-reports-list td span {
	color: #B4B2B2;
	display: inline-block;
	width: 25px;
}
.ajax-search-reports .report-note {
	font-weight: 500;
	color: #111;
}
#commercekit-form .tab-content {
	display: block !important;
	width: 100%;
}
#gallery-layout-preview {
	width: 140px;
	height: auto;
	min-height: 140px;
	border: 2px solid #c5e6fe;
	background: #effaff;
	margin-top: 15px;
	padding: 10px;
	border-radius: 6px;
}
#horizontal-preview .grid-full {
	width: 140px;
	height: 110px;
	background: #82ccf5;
	margin-bottom: 5px;
}
#horizontal-preview .grid-small {
	width: 140px;
	height: 25px;
	background: #82ccf5;
}
#vertical-left-preview {
	height: 140px;
}
#vertical-left-preview .grid-small {
	width: 25px;
	height: 140px;
	background: #82ccf5;
	margin-right: 5px;
	display: inline-block;
}
#vertical-left-preview .grid-full {
	width: 110px;
	height: 140px;
	background: #82ccf5;
	display: inline-block;
}
#vertical-right-preview {
	height: 140px;
}
#vertical-right-preview .grid-full {
	width: 110px;
	height: 140px;
	background: #82ccf5;
	margin-right: 5px;
	display: inline-block;
}
#vertical-right-preview .grid-small {
	width: 25px;
	height: 140px;
	background: #82ccf5;
	display: inline-block;
}
#vertical-scroll-preview {
	height: 140px;
}
#vertical-scroll-preview .grid-small {
	width: 25px;
	height: 140px;
	background: #82ccf5;
	margin-right: 5px;
	display: inline-block;
}
#vertical-scroll-preview .grid-full {
	width: 110px;
	height: 140px;
	display: inline-block;
}
#vertical-scroll-preview .grid-full .grid-1 {
	width: 110px;
	height: 67.5px;
	margin-bottom: 5px;
	background: #82ccf5;
}
#simple-scroll-preview {
	height: 140px;
}
#simple-scroll-preview .grid-full {
	width: 140px;
	height: 140px;
	display: inline-block;
}
#simple-scroll-preview .grid-full .grid-1 {
	width: 140px;
	height: 67.5px;
	margin-bottom: 5px;
	background: #82ccf5;
}
#gallery-layout-preview .grid-1 {
	width: 140px;
	height: 65px;
	margin-right: 5px;
	margin-bottom: 5px;
	background: #82ccf5;
}
#gallery-layout-preview .grid-2 {
	width: 68px;
	height: 32px;
	margin-right: 4px;
	margin-bottom: 0px;
	background: #82ccf5;
	display: inline-block;
}
#gallery-layout-preview .grid-2:nth-child(even) {
	margin-right: 0px;
}
#gallery-layout-preview .grid-3 {
	width: 44px;
	height: 33.5px;
	margin-right: 4px;
	margin-bottom: 0px;
	background: #82ccf5;
	display: inline-block;
}
#gallery-layout-preview .grid-3:nth-child(3) {
	margin-right: 0px;
}
#gallery-layout-preview #grid-1-2-2-preview .grid-1 {
	height: 67px;
}
#grid-2-4-preview {
	height: 140px;
}
#grid-3-1-2-preview {
	height: 140px;
}
#grid-1-2-2-preview {
	height: 140px;
}
.disable-as-plp {
	opacity: 0.25;
	pointer-events: none;
}
@media(max-width: 1400px) {
	.ckit-features-grid {
		grid-template-columns: repeat(1, minmax(0, 1fr));
	}
	ul.order-bump-statistics li {
		width: calc(100% - 20px);
    display: block;
    border: none;
	}
}
#cgkit-as-plp-options .cache-event-alert {
	display: none;
	margin-top: 10px;
}
#cgkit-as-plp-options .cache-event-alert#import-completed span svg {
	position: relative;
	top: 4px;
} 
#cgkit-as-logger.disable-events,
#cgkit-ajs-logger.disable-events {
	opacity: 0.7;
	pointer-events: none;
}
#product-countdown .product-dates label {
	display: inline-block;
	vertical-align: top;
	margin-right: 20px;
}
#product-countdown .end-inputs.disable-events {
	opacity: 0.5;
	pointer-events: none;
}
#product-countdown .product-dates label .input-error {
	display: none;
}
table td:has(.countdown-shortcode-id-wrapper) {
	padding-left: 0;
}
.countdown-shortcode-id-wrapper {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.countdown-shortcode-id-wrapper  .countdown-shortcode {
	box-shadow: 0 1px 1px rgba(0, 0, 0, .04);
  padding: 10px 12px;
  border: 1px solid #72d07a;
  border-radius: 6px;
  background: #f1faf2;
  font-size: 12px;
}
.single-campaign select {
	margin-top: 10px;
}
.single-campaign td:first-child {
	padding-left: 0;
}
.single-campaign .ends-label {
	display: flex;
	align-items: center;
	margin-top: 10px;
}
.cgkit-color-abs-wrap .wp-picker-holder {
	position: absolute;
	z-index: 999;
}
.cart-total-wrapper {
	display: flex;
	flex-wrap: wrap;
}
td.duplicate-orderbump {
	padding-left: 0;
}
.wp-core-ui td.duplicate-orderbump .button-primary {
	font-size: 12.5px;
}
.admin-order-bump .cart-total label {
	display: flex;
	align-items: center;
	justify-content: center;
}
.admin-order-bump .cart-total label.and-text {
	width: 50px;
	min-width: 50px;
}
.admin-order-bump .cart-total input {
	width: 100%;
	max-width: 100px;
}
#settings-content .dashboard h2 .table-heading {
	display: flex;
	align-items: center;
	justify-content: center;
}
#mobile-layout-preview {
	width: 140px;
	height: auto;
	min-height: 140px;
	border: 2px solid #c5e6fe;
	background: #effaff;
	margin-top: 15px;
	padding: 10px;
	border-radius: 6px;
}
#default-preview .grid-full {
	width: 140px;
	height: 110px;
	background: #82ccf5;
	margin-bottom: 5px;
}
#default-preview .grid-small {
	width: 140px;
	height: 25px;
	background: #82ccf5;
}
#minimal-preview .grid-full {
	width: 140px;
	height: 140px;
	background: #82ccf5;
}
#show-edge-preview {
	display: flex;
	flex-wrap: wrap;
}
#show-edge-preview .grid-full {
	width: 120px;
	height: 127px;
	background: #82ccf5;
	margin-right: 5px;
}
#show-edge-preview .grid-show-edge {
	width: 15px;
	height: 127px;
	background: #82ccf5;
}
#minimal-preview .grid-full {
	height: 127px;
}
.mobile-layout-preview .dots {
	display: flex;
	width: 100%;
	justify-content: center;
	align-items: center;
	position: relative;
	top: 8px;
}
.mobile-layout-preview .dots .dot {
	width: 6px;
	height: 6px;
	border-radius: 50%;
	background: #82ccf5;
	margin: 0 2px;
}
/* Responsive */
@media (max-width: 1300px) {
	body #settings-content {
		width: 100%;
	}
	body #settings-note {
		width: 100%;
		margin-left: 0;
		box-sizing: border-box;
	}
	.commercekit-logo {
		height: 40px;
	}
	.ajax-search-reports-boxes {
		grid-template-columns: repeat(1, minmax(0, 1fr));
		row-gap: 20px;
	}
}
@media screen and (max-width: 1024px) {
	.ckit-admin-wrapper {
		flex-direction: column;
	}
	.ckit-admin-wrapper .nav-tab-wrapper {
		box-sizing: border-box;
		width: 100%;
		position: inherit;
		top: auto;
		margin-bottom: 25px;
		display: grid;
		grid-template-columns: repeat(2, minmax(0, 1fr));
		column-gap: 25px;
		grid-template-rows: repeat(9, 1fr);
    grid-auto-flow: column;
	}
	.nav-tab-wrapper .nav-item-support {
		margin: 0;
	}
	.nav-tab-wrapper .nav-item-support:before {
		display: none;
	}
}
@media screen and (max-width: 782px) {
	.postbox .inside,
	.content-box h2 {
		padding-left: 25px;
		padding-right: 25px;
	}
	body.toplevel_page_commercekit.auto-fold #wpcontent {
		padding-left: 0;
	}
	.form-table th {
		padding-bottom: 0;
		width: 100%;
	}
	.form-table td {
		padding-left: 0;
		padding-right: 0;
	}
	body .form-table td label.text-label {
		width: 100%;
	}
	.form-table td>label:first-child {
		margin-top: 0;
	}
	.wp-core-ui .button {
		line-height: 2.1;
	}
}

/* -- RTL -- */
body.rtl {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}
.rtl #wpcontent { padding-right:0 }
.rtl .commercekit-logo {
	margin-left: 1rem;
	margin-right: 0;
}
.rtl .commercekit-admin-header--links div,
.rtl .commercekit-admin-header--links a {
    margin-left: 0.85rem;
    margin-right: 0;
}
.rtl .commercekit-admin-header--links a.button-primary {
	margin-left: 0;
}
.rtl .commercekit-admin-header--links div {
	padding-left: 0.75rem;
  border-left: 1px solid #e2e2e2;
  padding-right: 0;
  border-right: 0;
}
.rtl .ckit-admin-wrapper .nav-tab-wrapper {
	margin-left: 20px;
	margin-right: 0;
}
.rtl #settings-note {
	margin-right: 15px;
	margin-left: 0;
}
.rtl .ckit-features-grid section.active .status .active.shortcode {
	margin-right: 70px;
	margin-left: 0;
}
.rtl ul.order-bump-statistics li {
	border-left: 2px solid #f1f1f1;
	border-right: none;
}
.rtl ul.order-bump-statistics li:last-child {
	border-left: none;
}
.rtl .ckit-features-grid section .status .active,
.rtl .ckit-features-grid section .status .inactive {
	right: 18px;
	left: auto;
}
.rtl .ckit-perf svg,
.rtl .ckit-connection-status svg,
.rtl .cache-value.completed svg {
	margin-left: 6px;
	margin-right: 0;
}
.rtl .toggle-switch {
	margin-left: 10px;
	margin-right: 0;
}
.rtl ul.subtabs li a {
	margin-left: 0.5rem;
	margin-right: 0;
}
.rtl .postbox .handlediv {
	float: left;
}
.rtl .tooltip-wrapper {
	margin-right: 0.25rem;
	margin-left: 0;
}
.rtl .mini-explainer.cgkit-shortcode-help {
	padding-right: 50px;
	padding-left: 0;
}
.rtl .mini-explainer.cgkit-shortcode-help svg {
	right: 15px;
	left: inherit;
}
.rtl .form-table th {
	padding-left: 10px;
	padding-right: 0;
}
.rtl table .left {
	text-align: right;
}
.rtl table .right {
	text-align: left;
}
.rtl .form-table .w-120 input[type=radio],
.rtl .form-table .w-150 input[type=radio] {
	margin: 0 0px 0 8px;
}
.rtl.wp-core-ui .button-primary.clear-cache {
	margin-right: 50px;
	margin-left: 0;
}
.wishlist-display-wrap {
	margin: 5px 0px;
}
.wishlist-display-wrap input[type=radio] {
	width: auto;
}
#reset-waitlist-statistics,
#reset-wishlist-statistics {
	float: right;
}
#reset-ajs-statistics {
	margin-top: 10px;
}
#reset-ajs-modal {
	position: relative;
	margin-top: 15px;
	padding: 15px;
	background: #fcf9ef;
	border: 1px solid #fcc577;
	border-radius: 4px;
}
#reset-ajs-yes {
	margin-right: 15px;
}
.reset-ajs-modal-action {
	margin-top: 15px;
}