.widget_product_search.commercekit-ajs-active,.widget_search.commercekit-ajs-active{overflow:visible}.commercekit-ajs-results{position:absolute;overflow-y:auto;min-height:280px;overflow-x:hidden}.header-4 .commercekit-ajs-results{z-index:3;}.commercekit-ajs-suggestions{top:0;left:0;bottom:0;right:0;background:#fff;border:1px solid #d6d6d6}.commercekit-ajs-suggestions>.autocomplete-suggestion{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;cursor:pointer;border-bottom:1px solid #d6d6d6;background:0 0;display:flex;transition:.2s all}.commercekit-ajs-suggestions>.autocomplete-suggestion:hover,.commercekit-ajs-suggestions>.autocomplete-suggestion.active{background-color:#F8F8F8 }.commercekit-ajs-suggestions>.autocomplete-suggestion:last-child{border-bottom:none;margin-bottom:10px}.commercekit-ajs-suggestions>.autocomplete-suggestion.autocomplete-selected>a{background-color:#f8f8f8}.commercekit-ajs-suggestions>.autocomplete-no-suggestion{line-height:40px;text-align:center;font-size:13px;}.commercekit-ajs-suggestions>.autocomplete-no-suggestion+.autocomplete-no-suggestion{display:none;}.commercekit-ajs-results.has-viewall-button{padding-bottom:30px}.commercekit-ajs-results.has-viewall-button .commercekit-ajs-suggestions{bottom:30px}.commercekit-ajs-view-all-holder{width: calc(100% + 2px);z-index:9999;}.commercekit-ajs-view-all-holder+.autocomplete-no-suggestion{display:none;}.commercekit-ajs-view-all-holder>a{display:block;height:40px;line-height:40px;background:#373636;text-align:center;color:#fff;text-transform:uppercase;font-weight:600;font-size:11px;letter-spacing:.02em}.commercekit-ajs-view-all-holder>a:hover{color:#fff}.commercekit-ajs-other-result{height:33px;line-height:33px;background:#f5f3f3;color:#555;text-transform:uppercase;padding-left:10px;padding-right:10px;width:100%;font-size:11px;letter-spacing:.02em}.commercekit-ajs-hide .commercekit-ajs-suggestions,.commercekit-ajs-hide .commercekit-ajs-view-all-holder{display:none!important}.commercekit-ajs-suggestions .commercekit-ajs-post,.commercekit-ajs-suggestions .commercekit-ajs-product{width:100%;color:#323232}.commercekit-ajs-post .commercekit-ajs-post-image,.commercekit-ajs-product .commercekit-ajs-product-image{display:inline-flex;max-width:60px;vertical-align:top;flex-shrink:0;align-self: start;}.commercekit-ajs-post .commercekit-ajs-post-image img,.commercekit-ajs-product .commercekit-ajs-product-image img{padding:5px 5px 5px 0;margin:0}.commercekit-ajs-post .commercekit-ajs-post-title{display:inline-block;vertical-align:top;padding:0 10px;}.commercekit-ajs-product-desc{display:flex;flex-direction:column;justify-content:center;min-width:0;padding:0 10px;}.commercekit-ajs-post .commercekit-ajs-post-title{padding:0;display:flex;align-items:center}.autocomplete-suggestion a{display:flex;padding:10px;transition:.2s all}.commercekit-ajs-product-title{font-size:13px;line-height:1.4;font-weight:bold;}.commercekit-ajs-product .commercekit-ajs-product-desc{display:flex;flex-direction:column;justify-content:center;}.commercekit-ajs-product-price{font-size:13px}.commercekit-ajs-post .commercekit-ajs-post-title{font-size:13px;line-height:1.4;width:100%}.commercekit-ajs-post .commercekit-ajs-post-title span.post-type{text-transform:uppercase;color:#999;font-size:11px;margin-left:auto;padding-left:10px}.commercekit-ajs-other-result-wrap{cursor:auto!important;padding:0!important;margin:0!important;border:none}.commercekit-ajs-product-price,.commercekit-ajs-product-price ins{color:#de9915;font-weight:600}.commercekit-ajs-product-price del{margin-right:5px;color:#999;font-weight:400}.commercekit-ajs-results span.match-text{text-decoration:underline;display:contents}.commercekit-ajs-results span.product-short-desc{color:#323232;display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;line-height:20px;overflow:hidden;font-weight:normal;}.commercekit-ajs-results::-webkit-scrollbar{width:5px;}.commercekit-ajs-results::-webkit-scrollbar-track{background:#f1f1f1;}.commercekit-ajs-results::-webkit-scrollbar-thumb{background:#888;}.commercekit-ajs-results::-webkit-scrollbar-thumb:hover{background:#555;}
.keyboard-active .commercekit-ajs-suggestions .commercekit-ajs-product:focus-visible, .keyboard-active input.commercekit-ajax-search{outline-offset: -2px;}
.autocomplete-suggestion:has(+ .commercekit-ajs-view-all-holder) {border-bottom: 0;}
/* RTL */
.rtl .commercekit-ajs-post .commercekit-ajs-post-title span.post-type {margin-left: 0;margin-right: auto;padding-left: 0;padding-right: 10px;}