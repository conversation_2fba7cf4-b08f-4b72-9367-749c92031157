/* Countdown CSS */
#commercekit-timer.product { width: 50%; float: left; margin-right: 3%; margin-bottom: 10px;}
#commercekit-timer.product.has-cg-inventory { border-right: 1px solid #e2e2e2; }
#commercekit-timer.product .commercekit-timer-title { width: 100%; font-size: 13px; margin-bottom: 2px; }
#commercekit-timer.product .commercekit-timer-blocks { display: flex; white-space: nowrap; }
#commercekit-timer.product .commercekit-timer-block, #commercekit-timer.product .commercekit-timer-sep { display: inline-block; vertical-align: top; text-align: center; }
#commercekit-timer.product .commercekit-timer-digit, #commercekit-timer.product .commercekit-timer-sep { font-size: 22px; line-height: 26px; margin: 0px 2px; }
#commercekit-timer.product .commercekit-timer-label { font-size: 12px; color: #555; margin-bottom: -5px;}
#commercekit-timer.product .commercekit-timer-block { min-width: 32px; }
#commercekit-timer-message.product { font-size: 13px; padding: 0.5rem 0.75rem; background: #eee; margin-bottom: 0.75rem; }
#commercekit-timer.non-product, #commercekit-timer-message.non-product { width: 100%; padding: 10px; background: #f8f6db; border: 1px solid #dfda9e; border-radius: 4px; text-align: center; font-size: 14px; color: #111; font-weight: 600; clear: both; margin-bottom: 20px; }
.cgkit-timer-checkout-shortcode #commercekit-timer.non-product,
.cgkit-timer-checkout-shortcode #commercekit-timer-message.non-product { margin: 0 }
#commercekit-timer.non-product .commercekit-timer-title, #commercekit-timer.non-product .commercekit-timer-blocks, #commercekit-timer.non-product .commercekit-timer-block, #commercekit-timer.non-product .commercekit-timer-sep, #commercekit-timer.non-product .commercekit-timer-digit, #commercekit-timer.non-product .commercekit-timer-label { display: inline-flex; }
#commercekit-timer.non-product { display: flex; justify-content: center; }
#commercekit-timer.non-product .commercekit-timer-sep { display: none; }
#commercekit-timer.non-product .commercekit-timer-digit { margin-left: 5px; }
#commercekit-timer.non-product .commercekit-timer-label { margin-left: 3px; }
@media (max-width: 500px) { 
	#commercekit-timer.product { display: block; width: 100%; float: none; } #commercekit-timer.product.has-cg-inventory { 
border: none;}
#commercekit-timer.non-product { display: block; justify-content: center; }
}
.cgkit-timer-product-shortcode #commercekit-timer.product { float: none; width: auto; margin: 0 }
.cgkit-timer-product-shortcode #commercekit-timer.product.has-cg-inventory { border: none; }
.woocommerce-order-received .cgkit-timer-checkout-shortcode #commercekit-timer-message.non-product { display: none !important; } /* Do not show on thank you page */
/* -- RTL -- */
.rtl #commercekit-timer-wrap:not(.cgkit-timer-product-shortcode) #commercekit-timer.product {float: right;margin-right: 0;margin-left: 3%;}
.rtl #commercekit-timer-wrap:not(.cgkit-timer-product-shortcode) #commercekit-timer.product.has-cg-inventory {border-left: 1px solid #e2e2e2;border-right: none;} 